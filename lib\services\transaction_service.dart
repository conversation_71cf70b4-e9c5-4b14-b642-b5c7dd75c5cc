import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/core/services/supabase_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:uuid/uuid.dart';

class TransactionService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();
  LoanService? _loanService;

  List<Transaction> _transactions = [];
  bool _isLoading = false;

  TransactionService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadTransactions();
    }
  }

  void setLoanService(LoanService loanService) {
    _loanService = loanService;
  }

  List<Transaction> get transactions => _transactions;
  bool get isLoading => _isLoading;

  /// Get transactions for current month (using Solar Hijri calendar)
  List<Transaction> get currentMonthTransactions {
    final now = DateTime.now();
    return _transactions
        .where((t) => DateFormatter.isSameMonth(t.date, now))
        .toList();
  }

  /// Get income transactions for current month
  List<Transaction> get currentMonthIncome {
    return currentMonthTransactions.where((t) => t.type == 'درآمد').toList();
  }

  /// Get expense transactions for current month
  List<Transaction> get currentMonthExpenses {
    return currentMonthTransactions.where((t) => t.type == 'مصرف').toList();
  }

  /// Get total income for current month
  double get totalCurrentMonthIncome {
    return currentMonthIncome.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses for current month
  double get totalCurrentMonthExpenses {
    return currentMonthExpenses.fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get net savings for current month
  double get currentMonthNetSavings {
    return totalCurrentMonthIncome - totalCurrentMonthExpenses;
  }

  /// Get transactions for a specific date range
  List<Transaction> getTransactionsForDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return _transactions.where((t) {
      if (startDate != null && t.date.isBefore(startDate)) return false;
      if (endDate != null && t.date.isAfter(endDate)) return false;
      return true;
    }).toList();
  }

  /// Get income transactions for a specific date range
  List<Transaction> getIncomeForDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return getTransactionsForDateRange(
      startDate,
      endDate,
    ).where((t) => t.type == 'درآمد').toList();
  }

  /// Get expense transactions for a specific date range
  List<Transaction> getExpensesForDateRange(
    DateTime? startDate,
    DateTime? endDate,
  ) {
    return getTransactionsForDateRange(
      startDate,
      endDate,
    ).where((t) => t.type == 'مصرف').toList();
  }

  /// Get total income for a specific date range
  double getTotalIncomeForDateRange(DateTime? startDate, DateTime? endDate) {
    return getIncomeForDateRange(
      startDate,
      endDate,
    ).fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses for a specific date range
  double getTotalExpensesForDateRange(DateTime? startDate, DateTime? endDate) {
    return getExpensesForDateRange(
      startDate,
      endDate,
    ).fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get net savings for a specific date range
  double getNetSavingsForDateRange(DateTime? startDate, DateTime? endDate) {
    return getTotalIncomeForDateRange(startDate, endDate) -
        getTotalExpensesForDateRange(startDate, endDate);
  }

  /// Get all-time financial summary (for dashboard)
  Map<String, double> get allTimeFinancialSummary {
    return {
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'netSavings': totalCapital,
    };
  }

  /// Get transactions for a specific loan
  List<Transaction> getTransactionsForLoan(String loanId) {
    return _transactions.where((t) => t.loanId == loanId).toList();
  }

  Future<void> _loadTransactions() async {
    if (_userId.isEmpty) return;

    _isLoading = true;
    notifyListeners();

    try {
      final response = await SupabaseService.instance.client
          .from('transactions')
          .select()
          .eq('user_id', _userId)
          .eq('is_deleted', false)
          .order('date', ascending: false);

      _transactions = (response as List)
          .map((json) => Transaction.fromJson(json))
          .toList();
    } catch (e) {
      debugPrint('Error loading transactions: $e');
      _transactions = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addTransaction(
    String description,
    double amount,
    String type,
    String categoryId, {
    String? loanId,
    DateTime? date,
  }) async {
    if (_userId.isEmpty) return;

    try {
      final newTransaction = Transaction()
        ..id = _uuid.v4()
        ..date = date ?? DateTime.now()
        ..description = description
        ..amount = amount
        ..type = type
        ..categoryId = categoryId
        ..loanId = loanId
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('transactions')
          .insert(newTransaction.toJson());

      await _loadTransactions();

      // Update loan statuses if this transaction is linked to a loan
      if (loanId != null && _loanService != null) {
        _loanService!.updateLoanStatuses();
      }
    } catch (e) {
      debugPrint('Error adding transaction: $e');
      rethrow;
    }
  }

  Future<void> updateTransaction(
    Transaction transaction,
    String description,
    double amount,
    String type,
    String categoryId, {
    String? loanId,
    DateTime? date,
  }) async {
    try {
      final oldLoanId = transaction.loanId;

      transaction.description = description;
      transaction.amount = amount;
      transaction.type = type;
      transaction.categoryId = categoryId;
      transaction.loanId = loanId;
      transaction.date = date ?? transaction.date;
      transaction.updatedAt = DateTime.now();

      await SupabaseService.instance.client
          .from('transactions')
          .update(transaction.toJson())
          .eq('id', transaction.id);

      await _loadTransactions();

      // Update loan statuses if this transaction was linked to a loan
      if ((oldLoanId != null || loanId != null) && _loanService != null) {
        _loanService!.updateLoanStatuses();
      }
    } catch (e) {
      debugPrint('Error updating transaction: $e');
      rethrow;
    }
  }

  Future<void> deleteTransaction(Transaction transaction) async {
    try {
      final loanId = transaction.loanId;

      // Soft delete
      await SupabaseService.instance.client
          .from('transactions')
          .update({
            'is_deleted': true,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', transaction.id);

      await _loadTransactions();

      // Update loan statuses if this transaction was linked to a loan
      if (loanId != null && _loanService != null) {
        _loanService!.updateLoanStatuses();
      }
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      rethrow;
    }
  }

  /// Get transactions by category for current month
  Map<String, double> getCurrentMonthExpensesByCategory() {
    final expenses = currentMonthExpenses;
    final Map<String, double> categoryTotals = {};

    for (final transaction in expenses) {
      categoryTotals[transaction.categoryId] =
          (categoryTotals[transaction.categoryId] ?? 0.0) + transaction.amount;
    }

    return categoryTotals;
  }

  /// Get total capital (all time income - all time expenses)
  double get totalCapital {
    final totalIncome = _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    final totalExpenses = _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);

    return totalIncome - totalExpenses;
  }

  /// Get total income (all time)
  double get totalIncome {
    return _transactions
        .where((t) => t.type == 'درآمد')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get total expenses (all time)
  double get totalExpenses {
    return _transactions
        .where((t) => t.type == 'مصرف')
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  /// Get salary transactions (assuming salary is in a specific category)
  List<Transaction> get salaryTransactions {
    return _transactions
        .where(
          (t) =>
              t.type == 'درآمد' &&
                  t.description.toLowerCase().contains('معاش') ||
              t.description.toLowerCase().contains('حقوق') ||
              t.description.toLowerCase().contains('salary'),
        )
        .toList();
  }

  /// Refresh transaction data (for pull-to-refresh functionality)
  Future<void> refresh() async {
    await _loadTransactions();
  }

  /// Get total salary received
  double get totalSalary {
    return salaryTransactions.fold<double>(0.0, (sum, t) => sum + t.amount);
  }
}
