import 'package:flutter/material.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/utils/currency_formatter.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:my_fincance_app/utils/shamsi_date_picker.dart';
import 'package:provider/provider.dart';

class BudgetsPage extends StatefulWidget {
  const BudgetsPage({super.key});

  @override
  State<BudgetsPage> createState() => _BudgetsPageState();
}

class _BudgetsPageState extends State<BudgetsPage> {
  bool _showCurrentMonthOnly = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بودجه‌بندی'),
        centerTitle: true,
        backgroundColor: Colors.blue.shade50,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _showCurrentMonthOnly
                  ? Icons.calendar_month
                  : Icons.calendar_view_month,
            ),
            onPressed: () {
              setState(() {
                _showCurrentMonthOnly = !_showCurrentMonthOnly;
              });
            },
            tooltip: _showCurrentMonthOnly
                ? 'نمایش همه ماه‌ها'
                : 'نمایش ماه جاری',
          ),
        ],
      ),
      backgroundColor: Colors.grey.shade50,
      body: Consumer2<BudgetService, CategoryService>(
        builder: (context, budgetService, categoryService, child) {
          // Show loading indicator while data is being fetched
          if (budgetService.isLoading || categoryService.isLoading) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text(
                    'در حال بارگذاری بودجه‌ها...',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          final budgets = _showCurrentMonthOnly
              ? budgetService.currentMonthBudgets
              : budgetService.budgets;

          if (budgetService.budgets.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.pie_chart, size: 80, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'هنوز بودجه‌ای تعریف نشده است',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'برای شروع، بودجه جدید اضافه کنید',
                    style: TextStyle(fontSize: 14, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          if (budgets.isEmpty && _showCurrentMonthOnly) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.calendar_today,
                    size: 80,
                    color: Colors.grey,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'برای ${DateFormatter.getCurrentMonthYear()} بودجه‌ای تعریف نشده است',
                    style: const TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showBudgetDialog(context),
                    child: const Text('تعریف بودجه ماه جاری'),
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // Summary card
              if (_showCurrentMonthOnly)
                _buildMonthlySummary(budgetService, categoryService),

              // Budget list
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await budgetService.refresh();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: budgets.length,
                    itemBuilder: (context, index) {
                      final budget = budgets[index];
                      return _buildBudgetCard(
                        budget,
                        budgetService,
                        categoryService,
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          _showBudgetDialog(context);
        },
        icon: const Icon(Icons.add),
        label: const Text('بودجه جدید'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Widget _buildMonthlySummary(
    BudgetService budgetService,
    CategoryService categoryService,
  ) {
    final budgetVsActual = budgetService.getCurrentMonthBudgetVsActual();
    final totalBudget = budgetVsActual.values.fold<double>(
      0.0,
      (sum, data) => sum + data['budget']!,
    );
    final totalSpent = budgetVsActual.values.fold<double>(
      0.0,
      (sum, data) => sum + data['actual']!,
    );
    final totalRemaining = totalBudget - totalSpent;

    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        elevation: 6,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                Colors.blue.withValues(alpha: 0.1),
                Colors.blue.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'خلاصه بودجه ${DateFormatter.getCurrentMonthYear()}',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        'کل بودجه',
                        totalBudget,
                        Colors.blue,
                        Icons.account_balance_wallet,
                      ),
                    ),
                    Expanded(
                      child: _buildSummaryItem(
                        'مصرف شده',
                        totalSpent,
                        Colors.orange,
                        Icons.shopping_cart,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                _buildSummaryItem(
                  'باقیمانده',
                  totalRemaining,
                  totalRemaining >= 0 ? Colors.green : Colors.red,
                  totalRemaining >= 0 ? Icons.savings : Icons.warning,
                  isFullWidth: true,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String label,
    double amount,
    Color color,
    IconData icon, {
    bool isFullWidth = false,
  }) {
    final widget = Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 4),
          Text(
            CurrencyFormatter.formatWithPersianDigits(amount),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );

    return isFullWidth ? widget : Expanded(child: widget);
  }

  Widget _buildBudgetCard(
    Budget budget,
    BudgetService budgetService,
    CategoryService categoryService,
  ) {
    final categoryName = _getCategoryName(categoryService, budget.categoryId);
    final progress = budgetService.getBudgetProgress(budget);
    final actualSpending = budgetService.getActualSpending(
      budget.categoryId,
      budget.period,
    );
    final remaining = budget.amount - actualSpending;
    final isExceeded = budgetService.isBudgetExceeded(budget);

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        categoryName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        DateFormatter.formatPersianMonthYear(budget.period),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isExceeded)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Text(
                      'تجاوز از بودجه',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Amount details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'بودجه',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        CurrencyFormatter.formatWithPersianDigits(
                          budget.amount,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'مصرف شده',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        CurrencyFormatter.formatWithPersianDigits(
                          actualSpending,
                        ),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'باقیمانده',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                      Text(
                        CurrencyFormatter.formatWithPersianDigits(remaining),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: remaining >= 0 ? Colors.green : Colors.red,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Progress bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'پیشرفت مصرف',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    Text(
                      '${(progress * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: isExceeded ? Colors.red : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress > 1.0 ? 1.0 : progress,
                  backgroundColor: Colors.grey.shade300,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isExceeded
                        ? Colors.red
                        : (progress > 0.8 ? Colors.orange : Colors.green),
                  ),
                  minHeight: 8,
                ),
                if (isExceeded) ...[
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: Colors.red.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.red, size: 16),
                        const SizedBox(width: 8),
                        Text(
                          'تجاوز: ${CurrencyFormatter.formatWithPersianDigits(actualSpending - budget.amount)}',
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),

            // Action buttons
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showBudgetDialog(context, budget: budget),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('ویرایش'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue,
                      side: const BorderSide(color: Colors.blue),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _showDeleteBudgetDialog(context, budget),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('حذف'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getCategoryName(CategoryService categoryService, String categoryId) {
    try {
      return categoryService.categories
          .firstWhere((c) => c.id == categoryId)
          .name;
    } catch (e) {
      return 'حذف شده';
    }
  }

  void _showBudgetDialog(BuildContext context, {Budget? budget}) {
    final formKey = GlobalKey<FormState>();
    final amountController = TextEditingController(
      text: budget?.amount.toString() ?? '',
    );
    String? selectedCategoryId = budget?.categoryId;
    DateTime selectedPeriod = budget?.period ?? DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              title: Row(
                children: [
                  Icon(
                    budget == null ? Icons.add : Icons.edit,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  Text(budget == null ? 'افزودن بودجه جدید' : 'ویرایش بودجه'),
                ],
              ),
              content: SingleChildScrollView(
                child: Form(
                  key: formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Category dropdown
                      Consumer<CategoryService>(
                        builder: (context, categoryService, child) {
                          final expenseCategories = categoryService.categories
                              .where((c) => c.type == 'مصرف')
                              .toList();

                          return DropdownButtonFormField<String>(
                            value: selectedCategoryId,
                            decoration: const InputDecoration(
                              labelText: 'دسته‌بندی',
                              prefixIcon: Icon(Icons.folder),
                              border: OutlineInputBorder(),
                            ),
                            hint: const Text('انتخاب دسته‌بندی'),
                            items: expenseCategories.map((Category category) {
                              return DropdownMenuItem<String>(
                                value: category.id,
                                child: Text(category.name),
                              );
                            }).toList(),
                            onChanged: (newValue) {
                              setState(() {
                                selectedCategoryId = newValue;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'لطفاً دسته‌بندی را انتخاب کنید';
                              }
                              return null;
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 16),

                      // Amount field
                      TextFormField(
                        controller: amountController,
                        decoration: const InputDecoration(
                          labelText: 'مبلغ بودجه (افغانی)',
                          prefixIcon: Icon(Icons.attach_money),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'لطفاً مبلغ بودجه را وارد کنید';
                          }
                          if (double.tryParse(value) == null ||
                              double.parse(value) <= 0) {
                            return 'لطفاً مبلغ معتبر وارد کنید';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Period picker
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: ListTile(
                          leading: const Icon(Icons.calendar_today),
                          title: const Text('دوره بودجه'),
                          subtitle: Text(
                            DateFormatter.formatPersianMonthYear(
                              selectedPeriod,
                            ),
                          ),
                          onTap: () async {
                            final selectedDate =
                                await ShamsiDatePicker.showShamsiDatePicker(
                                  context: context,
                                  initialDate: selectedPeriod,
                                  firstDate: DateTime(1380, 1, 1),
                                  lastDate: DateTime.now().add(
                                    const Duration(days: 365),
                                  ), // Allow future dates for budget planning
                                  helpText: 'انتخاب دوره بودجه',
                                  confirmText: 'تأیید',
                                  cancelText: 'لغو',
                                );
                            if (selectedDate != null) {
                              setState(() {
                                selectedPeriod = selectedDate;
                              });
                            }
                          },
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Info card
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: Colors.blue.withValues(alpha: 0.3),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue, size: 20),
                            SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'بودجه برای کنترل مصارف ماهانه در هر دسته‌بندی تعریف می‌شود',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('لغو'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      if (budget == null) {
                        Provider.of<BudgetService>(
                          context,
                          listen: false,
                        ).addBudget(
                          selectedPeriod,
                          double.parse(amountController.text),
                          selectedCategoryId!,
                        );
                      } else {
                        Provider.of<BudgetService>(
                          context,
                          listen: false,
                        ).updateBudget(
                          budget,
                          selectedPeriod,
                          double.parse(amountController.text),
                        );
                      }
                      Navigator.of(context).pop();

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(
                            budget == null
                                ? 'بودجه جدید با موفقیت ثبت شد'
                                : 'بودجه با موفقیت ویرایش شد',
                          ),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  },
                  child: Text(budget == null ? 'ثبت' : 'ویرایش'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteBudgetDialog(BuildContext context, Budget budget) {
    final categoryService = Provider.of<CategoryService>(
      context,
      listen: false,
    );
    final categoryName = _getCategoryName(categoryService, budget.categoryId);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('حذف بودجه'),
            ],
          ),
          content: Text(
            'آیا مطمئن هستید که می‌خواهید بودجه "$categoryName" را حذف کنید؟',
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('لغو'),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              onPressed: () {
                Provider.of<BudgetService>(
                  context,
                  listen: false,
                ).deleteBudget(budget);
                Navigator.of(context).pop();

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('بودجه با موفقیت حذف شد'),
                    backgroundColor: Colors.red,
                  ),
                );
              },
              child: const Text('حذف', style: TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );
  }
}
