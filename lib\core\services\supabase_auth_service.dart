import 'package:flutter/widgets.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/user.dart' as model;
import 'supabase_service.dart';

/// Supabase authentication service for cloud-only architecture
class SupabaseAuthService extends ChangeNotifier {
  model.User? _currentUser;
  bool _isInitialized = false;

  model.User? get currentUser => _currentUser;
  bool get isLoggedIn =>
      _currentUser != null &&
      SupabaseService.instance.client.auth.currentUser != null;
  String get currentUserId => _currentUser?.id ?? '';
  bool get isInitialized => _isInitialized;

  /// Initialize the auth service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Listen to auth state changes
      SupabaseService.instance.client.auth.onAuthStateChange.listen((data) {
        _handleAuthStateChange(data.event, data.session);
      });

      // Check if user is already logged in
      final session = SupabaseService.instance.client.auth.currentSession;
      if (session != null) {
        await _loadUserFromSession(session);
      }

      _isInitialized = true;

      // Use post-frame callback to avoid calling notifyListeners during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    } catch (e) {
      debugPrint('Failed to initialize SupabaseAuthService: $e');
      _isInitialized =
          true; // Mark as initialized even if failed to avoid infinite loops
      WidgetsBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
    }
  }

  /// Handle authentication state changes
  void _handleAuthStateChange(AuthChangeEvent event, Session? session) async {
    debugPrint('Auth state changed: $event');

    switch (event) {
      case AuthChangeEvent.signedIn:
        if (session != null) {
          await _loadUserFromSession(session);
        }
        break;
      case AuthChangeEvent.signedOut:
        await _handleSignOut();
        break;
      case AuthChangeEvent.userUpdated:
        if (session != null) {
          await _loadUserFromSession(session);
        }
        break;
      case AuthChangeEvent.initialSession:
        if (session != null) {
          await _loadUserFromSession(session);
        }
        break;
      case AuthChangeEvent.passwordRecovery:
        // Handle password recovery if needed
        break;
      case AuthChangeEvent.tokenRefreshed:
        // Token refreshed, user data should still be valid
        break;
      default:
        break;
    }

    // Use post-frame callback to avoid calling notifyListeners during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      notifyListeners();
    });
  }

  /// Load user from Supabase session
  Future<void> _loadUserFromSession(Session session) async {
    try {
      final supabaseUser = session.user;

      // Fetch user profile from Supabase
      final response = await SupabaseService.instance.client
          .from('users')
          .select()
          .eq('id', supabaseUser.id)
          .single();

      // Create user model from Supabase data
      final user = model.User.fromJson(response);
      _currentUser = user;
    } catch (e) {
      debugPrint('Error loading user from session: $e');
      // If user doesn't exist in users table, create one
      await _createUserProfile(session.user);
    }
  }

  /// Create user profile in Supabase
  Future<void> _createUserProfile(User supabaseUser) async {
    try {
      final userData = {
        'id': supabaseUser.id,
        'email': supabaseUser.email ?? '',
        'name': supabaseUser.userMetadata?['name'] ?? 'User',
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await SupabaseService.instance.client.from('users').insert(userData);

      // Load the newly created user
      final user = model.User.fromJson(userData);
      _currentUser = user;
    } catch (e) {
      debugPrint('Error creating user profile: $e');
    }
  }

  /// Handle sign out
  Future<void> _handleSignOut() async {
    _currentUser = null;
  }

  /// Register a new user with Supabase
  Future<bool> register(String email, String password, String name) async {
    try {
      if (!SupabaseService.instance.isOnline) {
        throw Exception('No internet connection');
      }

      final response = await SupabaseService.instance.client.auth.signUp(
        email: email,
        password: password,
        data: {'name': name},
      );

      if (response.user != null) {
        // User will be loaded via auth state change
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Registration failed: $e');
      return false;
    }
  }

  /// Login user with Supabase
  Future<bool> login(String email, String password) async {
    try {
      if (!SupabaseService.instance.isOnline) {
        throw Exception(
          'No internet connection. Cloud-only mode requires internet.',
        );
      }

      final response = await SupabaseService.instance.client.auth
          .signInWithPassword(email: email, password: password);

      if (response.user != null) {
        // User will be loaded via auth state change
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('Login failed: $e');

      // Provide more specific error information for debugging
      if (e.toString().contains('Failed to fetch')) {
        debugPrint(
          'Network connectivity issue detected. Check internet connection and Supabase URL accessibility.',
        );
      } else if (e.toString().contains('Invalid login credentials')) {
        debugPrint('Invalid email or password provided.');
      } else if (e.toString().contains('Email not confirmed')) {
        debugPrint('Email confirmation required.');
      }

      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    try {
      if (SupabaseService.instance.isOnline) {
        await SupabaseService.instance.client.auth.signOut();
      } else {
        await _handleSignOut();
      }
    } catch (e) {
      debugPrint('Logout failed: $e');
      await _handleSignOut();
    }
  }

  /// Update user profile
  Future<bool> updateProfile(String name) async {
    try {
      if (_currentUser == null) return false;

      if (!SupabaseService.instance.isOnline) {
        throw Exception(
          'No internet connection. Cloud-only mode requires internet.',
        );
      }

      // Update on server
      await SupabaseService.instance.client.auth.updateUser(
        UserAttributes(data: {'name': name}),
      );

      // Update user profile in users table
      await SupabaseService.instance.client
          .from('users')
          .update({
            'name': name,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', _currentUser!.id);

      // Update local user object
      _currentUser!.name = name;
      _currentUser!.updatedAt = DateTime.now();
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('Profile update failed: $e');
      return false;
    }
  }

  /// Change password
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      if (_currentUser == null) return false;

      if (!SupabaseService.instance.isOnline) {
        throw Exception(
          'No internet connection. Cloud-only mode requires internet.',
        );
      }

      // Update password on server
      await SupabaseService.instance.client.auth.updateUser(
        UserAttributes(password: newPassword),
      );

      return true;
    } catch (e) {
      debugPrint('Password change failed: $e');
      return false;
    }
  }
}
